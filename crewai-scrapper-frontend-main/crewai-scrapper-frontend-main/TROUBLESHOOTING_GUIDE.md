# 🔧 Troubleshooting Guide - AI Chat Authentication & Dispatch Issues

## 🚨 **Current Issues Identified:**

### **Issue 1: 401 Unauthorized Error**
```
POST http://localhost:5000/api/chat/conversations/6c57ede6-ba49-4f3c-971f-1e38d7921151/messages 401 (Unauthorized)
```

### **Issue 2: Redux Dispatch Error**
```
Error: Actions must be plain objects. Instead, the actual type was: 'Promise'. 
You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk'
```

## 🔍 **Diagnosis & Solutions:**

### **1. Authentication Issue (401 Error)**

**Root Cause:** User is not properly authenticated or token is expired/invalid.

**Quick Fixes:**

#### **A. Check Authentication Status**
1. Open browser console
2. Run: `localStorage.getItem('accessToken')`
3. Run: `localStorage.getItem('user')`
4. If either is null/undefined, user needs to log in

#### **B. Manual Login Test**
```javascript
// In browser console - test if you can get a valid token
fetch('http://localhost:5000/api/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email: 'your-email', password: 'your-password' })
})
.then(r => r.json())
.then(data => {
  console.log('Login response:', data);
  if (data.data?.accessToken) {
    localStorage.setItem('accessToken', data.data.accessToken);
    localStorage.setItem('user', JSON.stringify(data.data.user));
    console.log('✅ Tokens saved! Refresh the page.');
  }
});
```

#### **C. Backend Authentication Check**
Ensure the backend auth middleware is working:
```bash
# Test the auth endpoint
curl -X POST http://localhost:5000/api/chat/conversations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{"initialMessage": "test"}'
```

### **2. Redux Dispatch Error (Promise Issue)**

**Root Cause:** Trying to dispatch a Promise instead of a plain action object.

**Fixed in Code:** ✅ Already implemented the fix:
- Removed direct dispatch of Promise results
- Added proper error handling
- Separated async operations from dispatch calls

**Verification:**
```javascript
// In browser console - check if Redux DevTools shows proper actions
window.__REDUX_DEVTOOLS_EXTENSION__ && console.log('Redux DevTools available');
```

## 🛠 **Step-by-Step Debugging Process:**

### **Step 1: Verify Authentication**
1. **Check Auth Debug Component** (added to ChatInterface in development mode)
   - Look for the debug panel in bottom-right corner
   - Click "Log to Console" to see detailed auth info

2. **Manual Auth Check:**
   ```javascript
   // In browser console
   const token = localStorage.getItem('accessToken');
   const user = localStorage.getItem('user');
   console.log('Token exists:', !!token);
   console.log('User exists:', !!user);
   console.log('Token preview:', token?.substring(0, 20) + '...');
   ```

### **Step 2: Test Backend Connection**
1. **Check if backend is running:**
   ```bash
   curl http://localhost:5000/api/health
   ```

2. **Test auth endpoint:**
   ```bash
   curl -X POST http://localhost:5000/api/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password"}'
   ```

### **Step 3: Fix Authentication**
If authentication is broken:

1. **Clear old tokens:**
   ```javascript
   localStorage.removeItem('accessToken');
   localStorage.removeItem('user');
   ```

2. **Login again through the UI or manually**

3. **Verify new tokens are saved:**
   ```javascript
   console.log('New token:', localStorage.getItem('accessToken'));
   ```

### **Step 4: Test Chat Functionality**
1. **Open chat interface**
2. **Send a test message**
3. **Check browser console for errors**
4. **Verify API calls in Network tab**

## 🔧 **Quick Fixes Applied:**

### **1. Enhanced Error Handling**
- ✅ Added authentication checks before API calls
- ✅ Improved error logging and user feedback
- ✅ Added fallback error handling for different error types

### **2. Fixed Dispatch Issues**
- ✅ Separated async operations from Redux dispatch
- ✅ Added proper error state management
- ✅ Prevented Promise objects from being dispatched

### **3. Added Debug Tools**
- ✅ AuthDebug component for real-time auth status
- ✅ Enhanced console logging for troubleshooting
- ✅ Better error messages and status indicators

## 🚀 **Testing the Fixes:**

### **Test Scenario 1: Authenticated User**
1. Ensure user is logged in
2. Open chat interface
3. Send message: "test jeans"
4. **Expected:** AI processes message and creates conversation

### **Test Scenario 2: Unauthenticated User**
1. Clear localStorage tokens
2. Try to send chat message
3. **Expected:** Console shows "User not authenticated" message

### **Test Scenario 3: Token Refresh**
1. Use an expired token
2. Send chat message
3. **Expected:** Token refresh should happen automatically

## 📋 **Checklist for Resolution:**

- [ ] Backend server is running on port 5000
- [ ] User has valid authentication tokens
- [ ] Redux store is properly configured
- [ ] API calls include proper Authorization headers
- [ ] Error handling is working correctly
- [ ] Chat interface shows proper loading states

## 🆘 **If Issues Persist:**

1. **Check Backend Logs:**
   ```bash
   # In backend directory
   npm run dev
   # Look for authentication errors
   ```

2. **Verify Database Connection:**
   - Ensure database is running
   - Check if user exists in database

3. **Test with Postman/curl:**
   - Test login endpoint
   - Test chat endpoints with valid token

4. **Clear Browser Cache:**
   - Clear localStorage
   - Hard refresh (Ctrl+Shift+R)
   - Try incognito mode

## 🎯 **Expected Working Flow:**

1. **User Authentication** ✅
2. **Chat Interface Opens** ✅
3. **User Types Message** ✅
4. **AI Processes with Context** ✅
5. **Search Terms Generated** ✅
6. **Products Updated** ✅
7. **Context Maintained** ✅

The fixes implemented should resolve both the authentication and dispatch issues. Use the debug tools and follow the troubleshooting steps to verify everything is working correctly!
