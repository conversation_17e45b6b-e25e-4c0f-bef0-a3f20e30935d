import { Mutex } from "async-mutex";
import { setCredentials } from "./services/Auth/AuthSlice";
import { logoutUser } from "./services/Auth/AuthSlice";
import { baseQuery } from "./api-utils";
import { BaseQueryFn } from "@reduxjs/toolkit/query";

const mutex = new Mutex();

export const baseQueryWithReauth: BaseQueryFn = async (
  args,
  api,
  extraOptions
) => {
  await mutex.waitForUnlock();
  let result = await baseQuery(args, api, extraOptions);

  if (result.error && result.error.status === 401) {
    // Prevent multiple refresh requests
    if (!mutex.isLocked()) {
      const release = await mutex.acquire();

      try {
        const accessToken = localStorage.getItem("accessToken") || "";

        // Attempt to refresh token
        const refreshResult = await baseQuery(
          {
            url: "refresh-token",
            method: "GET",
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          },
          api,
          extraOptions
        );

        if (refreshResult.data) {
          // Extract the new access token from the response
          const newAccessToken = (refreshResult.data as { data: { accessToken: string } }).data.accessToken;

          // Update tokens in Redux and localStorage
          api.dispatch(setCredentials({
            user: JSON.parse(localStorage.getItem("user") || "{}"),
            accessToken: newAccessToken
          }));
          localStorage.setItem("accessToken", newAccessToken);

          // Retry the original request with the NEW token
          result = await baseQuery(
            {
              ...args,
              headers: {
                Authorization: `Bearer ${newAccessToken}`,
              },
            },
            api,
            extraOptions
          );
        } else {
          // Refresh failed, log out user
          api.dispatch(logoutUser());
        }
      } catch (error) {
        console.error("Token refresh failed:", error);
        api.dispatch(logoutUser());
      } finally {
        release();
      }
    } else {
      // Wait for the mutex to be released, then retry
      await mutex.waitForUnlock();
      const currentAccessToken = localStorage.getItem("accessToken") || "";
      result = await baseQuery(
        {
          ...args,
          headers: {
            Authorization: `Bearer ${currentAccessToken}`,
          },
        },
        api,
        extraOptions
      );
    }
  }

  return result;
};
