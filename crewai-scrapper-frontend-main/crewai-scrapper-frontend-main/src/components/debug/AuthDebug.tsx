import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../api/store";

const AuthDebug: React.FC = () => {
  const { user, accessToken, isAuthenticated } = useSelector((state: RootState) => state.auth);
  
  // Get tokens from localStorage for comparison
  const localStorageToken = localStorage.getItem("accessToken");
  const localStorageUser = localStorage.getItem("user");
  
  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-md z-50">
      <h3 className="text-sm font-bold mb-2">🔍 Auth Debug Info</h3>
      
      <div className="space-y-2 text-xs">
        <div>
          <strong>Redux State:</strong>
          <div className="ml-2">
            <div>isAuthenticated: <span className={isAuthenticated ? "text-green-600" : "text-red-600"}>{String(isAuthenticated)}</span></div>
            <div>user: {user ? "✅ Present" : "❌ Null"}</div>
            <div>accessToken: {accessToken ? "✅ Present" : "❌ Null"}</div>
          </div>
        </div>
        
        <div>
          <strong>localStorage:</strong>
          <div className="ml-2">
            <div>accessToken: {localStorageToken ? "✅ Present" : "❌ Null"}</div>
            <div>user: {localStorageUser ? "✅ Present" : "❌ Null"}</div>
          </div>
        </div>
        
        {accessToken && (
          <div>
            <strong>Token Preview:</strong>
            <div className="ml-2 text-xs font-mono bg-gray-100 p-1 rounded">
              {accessToken.substring(0, 20)}...
            </div>
          </div>
        )}
        
        {user && (
          <div>
            <strong>User Info:</strong>
            <div className="ml-2">
              <div>ID: {user.id}</div>
              <div>Email: {user.email}</div>
            </div>
          </div>
        )}
      </div>
      
      <div className="mt-3 pt-2 border-t border-gray-200">
        <button
          onClick={() => {
            console.log("=== AUTH DEBUG INFO ===");
            console.log("Redux Auth State:", { user, accessToken: !!accessToken, isAuthenticated });
            console.log("localStorage accessToken:", localStorageToken);
            console.log("localStorage user:", localStorageUser);
            console.log("Full accessToken:", accessToken);
            console.log("========================");
          }}
          className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
        >
          Log to Console
        </button>
      </div>
    </div>
  );
};

export default AuthDebug;
