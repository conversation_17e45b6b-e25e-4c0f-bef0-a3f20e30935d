import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../api/store";
import { getAuthDebugInfo, clearAuthTokens } from "../../utils/tokenUtils";

const AuthDebug: React.FC = () => {
  const { user, accessToken, isAuthenticated } = useSelector((state: RootState) => state.auth);

  // Get detailed auth debug info
  const debugInfo = getAuthDebugInfo();
  
  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-md z-50 max-h-96 overflow-y-auto">
      <h3 className="text-sm font-bold mb-2">🔍 Auth Debug Info</h3>

      <div className="space-y-2 text-xs">
        <div>
          <strong>Redux State:</strong>
          <div className="ml-2">
            <div>isAuthenticated: <span className={isAuthenticated ? "text-green-600" : "text-red-600"}>{String(isAuthenticated)}</span></div>
            <div>user: {user ? "✅ Present" : "❌ Null"}</div>
            <div>accessToken: {accessToken ? "✅ Present" : "❌ Null"}</div>
          </div>
        </div>

        <div>
          <strong>Token Status:</strong>
          <div className="ml-2">
            <div>hasToken: <span className={debugInfo.hasToken ? "text-green-600" : "text-red-600"}>{String(debugInfo.hasToken)}</span></div>
            <div>isExpired: <span className={debugInfo.isExpired ? "text-red-600" : "text-green-600"}>{String(debugInfo.isExpired)}</span></div>
            <div>isExpiringSoon: <span className={debugInfo.isExpiringSoon ? "text-yellow-600" : "text-green-600"}>{String(debugInfo.isExpiringSoon)}</span></div>
            {debugInfo.timeUntilExpiry !== undefined && (
              <div>timeUntilExpiry: <span className={debugInfo.timeUntilExpiry < 30 ? "text-red-600" : "text-green-600"}>{debugInfo.timeUntilExpiry}s</span></div>
            )}
            {debugInfo.expiryTime && (
              <div>expiryTime: {debugInfo.expiryTime}</div>
            )}
          </div>
        </div>

        {debugInfo.tokenPayload && (
          <div>
            <strong>Token Payload:</strong>
            <div className="ml-2 text-xs font-mono bg-gray-100 p-1 rounded">
              <div>userId: {debugInfo.tokenPayload.userId}</div>
              <div>iat: {debugInfo.tokenPayload.iat}</div>
              <div>exp: {debugInfo.tokenPayload.exp}</div>
            </div>
          </div>
        )}

        {user && (
          <div>
            <strong>User Info:</strong>
            <div className="ml-2">
              <div>ID: {user.id}</div>
              <div>Email: {user.email}</div>
            </div>
          </div>
        )}
      </div>
      
      <div className="mt-3 pt-2 border-t border-gray-200 flex space-x-2">
        <button
          onClick={() => {
            console.log("=== AUTH DEBUG INFO ===");
            console.log("Redux Auth State:", { user, accessToken: !!accessToken, isAuthenticated });
            console.log("Debug Info:", debugInfo);
            console.log("Full accessToken:", accessToken);
            console.log("========================");
          }}
          className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
        >
          Log to Console
        </button>

        <button
          onClick={() => {
            clearAuthTokens();
            window.location.reload();
          }}
          className="text-xs bg-red-500 text-white px-2 py-1 rounded hover:bg-red-600"
        >
          Clear Tokens
        </button>
      </div>
    </div>
  );
};

export default AuthDebug;
