// Token utility functions for handling JWT tokens

export interface TokenPayload {
  userId: string;
  refreshTokenId: string;
  iat: number;
  exp: number;
}

/**
 * Decode JWT token payload
 */
export const decodeToken = (token: string): TokenPayload | null => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload;
  } catch (error) {
    console.error('Failed to decode token:', error);
    return null;
  }
};

/**
 * Check if token is expired
 */
export const isTokenExpired = (token: string): boolean => {
  const payload = decodeToken(token);
  if (!payload || !payload.exp) {
    return true;
  }
  
  const currentTime = Math.floor(Date.now() / 1000);
  return payload.exp < currentTime;
};

/**
 * Check if token will expire soon (within 30 seconds)
 */
export const isTokenExpiringSoon = (token: string): boolean => {
  const payload = decodeToken(token);
  if (!payload || !payload.exp) {
    return true;
  }
  
  const currentTime = Math.floor(Date.now() / 1000);
  const timeUntilExpiry = payload.exp - currentTime;
  return timeUntilExpiry < 30; // Less than 30 seconds
};

/**
 * Get token expiry time in human readable format
 */
export const getTokenExpiryTime = (token: string): string => {
  const payload = decodeToken(token);
  if (!payload || !payload.exp) {
    return 'Invalid token';
  }
  
  const expiryDate = new Date(payload.exp * 1000);
  return expiryDate.toLocaleString();
};

/**
 * Get time until token expires
 */
export const getTimeUntilExpiry = (token: string): number => {
  const payload = decodeToken(token);
  if (!payload || !payload.exp) {
    return 0;
  }
  
  const currentTime = Math.floor(Date.now() / 1000);
  return Math.max(0, payload.exp - currentTime);
};

/**
 * Clear authentication tokens from localStorage
 */
export const clearAuthTokens = (): void => {
  localStorage.removeItem('accessToken');
  localStorage.removeItem('user');
  console.log('Authentication tokens cleared');
};

/**
 * Validate current authentication state
 */
export const validateAuthState = (): { isValid: boolean; reason?: string } => {
  const token = localStorage.getItem('accessToken');
  const user = localStorage.getItem('user');
  
  if (!token) {
    return { isValid: false, reason: 'No access token found' };
  }
  
  if (!user) {
    return { isValid: false, reason: 'No user data found' };
  }
  
  if (isTokenExpired(token)) {
    return { isValid: false, reason: 'Token is expired' };
  }
  
  return { isValid: true };
};

/**
 * Get authentication debug info
 */
export const getAuthDebugInfo = () => {
  const token = localStorage.getItem('accessToken');
  const user = localStorage.getItem('user');
  
  if (!token) {
    return { hasToken: false, hasUser: !!user };
  }
  
  const payload = decodeToken(token);
  const timeUntilExpiry = getTimeUntilExpiry(token);
  
  return {
    hasToken: true,
    hasUser: !!user,
    tokenPayload: payload,
    isExpired: isTokenExpired(token),
    isExpiringSoon: isTokenExpiringSoon(token),
    timeUntilExpiry,
    expiryTime: getTokenExpiryTime(token)
  };
};
