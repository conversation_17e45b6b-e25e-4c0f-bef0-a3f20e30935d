import { OpenAI } from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Helper function to extract product context from conversation
const extractProductContext = (messages, userMessage) => {
  const allText = [...messages.map(m => m.content), userMessage].join(' ').toLowerCase();

  // Common product categories
  const categories = ['jeans', 'pants', 'shirts', 'shoes', 'sneakers', 'boots', 'dress', 'jacket', 'sweater', 'hoodie', 'bag', 'handbag', 'watch', 'jewelry', 'necklace', 'ring'];
  const colors = ['black', 'white', 'blue', 'red', 'green', 'yellow', 'pink', 'purple', 'brown', 'gray', 'grey', 'navy', 'beige', 'tan'];
  const sizes = ['xs', 'small', 'medium', 'large', 'xl', 'xxl', '28', '30', '32', '34', '36', '38', '40'];
  const styles = ['skinny', 'slim', 'regular', 'loose', 'baggy', 'tight', 'fitted', 'oversized', 'vintage', 'casual', 'formal'];

  const foundCategories = categories.filter(cat => allText.includes(cat));
  const foundColors = colors.filter(color => allText.includes(color));
  const foundSizes = sizes.filter(size => allText.includes(size));
  const foundStyles = styles.filter(style => allText.includes(style));

  // Extract price mentions
  const priceMatch = allText.match(/under\s*\$?(\d+)|below\s*\$?(\d+)|less\s*than\s*\$?(\d+)|\$(\d+)/);
  const priceLimit = priceMatch ? priceMatch[1] || priceMatch[2] || priceMatch[3] || priceMatch[4] : null;

  return {
    categories: foundCategories,
    colors: foundColors,
    sizes: foundSizes,
    styles: foundStyles,
    priceLimit: priceLimit
  };
};

// Helper function to build contextual message
const buildContextualMessage = (previousSearchTerms, enhancedContext, userMessage) => {
  let contextualMessage = '';

  // Add search history context
  if (previousSearchTerms.length > 0) {
    contextualMessage += `Search History: ${previousSearchTerms.join(' → ')}. `;
  }

  // Add session context
  if (enhancedContext.searchHistory && enhancedContext.searchHistory.length > 0) {
    contextualMessage += `Session searches: ${enhancedContext.searchHistory.slice(-3).join(' → ')}. `;
  }

  // Add product context
  const contextParts = [];
  if (enhancedContext.categories.length > 0) {
    contextParts.push(`Categories: ${enhancedContext.categories.join(', ')}`);
  }
  if (enhancedContext.colors.length > 0) {
    contextParts.push(`Colors: ${enhancedContext.colors.join(', ')}`);
  }
  if (enhancedContext.styles.length > 0) {
    contextParts.push(`Styles: ${enhancedContext.styles.join(', ')}`);
  }
  if (enhancedContext.sizes.length > 0) {
    contextParts.push(`Sizes: ${enhancedContext.sizes.join(', ')}`);
  }

  if (contextParts.length > 0) {
    contextualMessage += `Product context - ${contextParts.join(', ')}. `;
  }

  // Add price context
  if (enhancedContext.priceLimit) {
    contextualMessage += `Price preference: under $${enhancedContext.priceLimit}. `;
  }

  // Add user intent history
  if (enhancedContext.userIntents && enhancedContext.userIntents.length > 0) {
    const recentIntents = enhancedContext.userIntents.slice(-3);
    contextualMessage += `Recent intents: ${recentIntents.join(', ')}. `;
  }

  contextualMessage += `User's current message: "${userMessage}"`;

  return contextualMessage;
};

// Helper function to merge product contexts
const mergeContexts = (currentContext, storedContext) => {
  if (!storedContext) return currentContext;

  return {
    categories: Array.from(new Set([
      ...currentContext.categories,
      ...(storedContext.sessionMetadata?.categories || [])
    ])),
    colors: Array.from(new Set([
      ...currentContext.colors,
      ...(storedContext.productContext?.colors || [])
    ])),
    sizes: Array.from(new Set([
      ...currentContext.sizes,
      ...(storedContext.productContext?.sizes || [])
    ])),
    styles: Array.from(new Set([
      ...currentContext.styles,
      ...(storedContext.productContext?.styles || [])
    ])),
    priceLimit: currentContext.priceLimit || storedContext.productContext?.priceLimit,
    userIntents: storedContext.userIntents || [],
    searchHistory: storedContext.searchHistory || []
  };
};

// Enhanced fallback search term generation
const generateFallbackSearchTerm = (messages, userMessage, conversationContext = null) => {
  const productContext = extractProductContext(messages, userMessage);
  const enhancedContext = mergeContexts(productContext, conversationContext);

  // Try to build a meaningful search term from context
  let searchTermParts = [];

  if (enhancedContext.colors.length > 0) {
    searchTermParts.push(enhancedContext.colors[0]);
  }

  if (enhancedContext.styles.length > 0) {
    searchTermParts.push(enhancedContext.styles[0]);
  }

  if (enhancedContext.categories.length > 0) {
    searchTermParts.push(enhancedContext.categories[0]);
  } else {
    // Extract key words from user message
    const words = userMessage.toLowerCase().split(' ').filter(word =>
      word.length > 2 && !['the', 'and', 'for', 'with', 'under', 'over'].includes(word)
    );
    if (words.length > 0) {
      searchTermParts.push(words[0]);
    }
  }

  if (enhancedContext.priceLimit) {
    searchTermParts.push(`under ${enhancedContext.priceLimit}`);
  }

  return searchTermParts.length > 0 ? searchTermParts.join(' ') : userMessage.substring(0, 50).trim();
};

export const generateSearchTermFromConversation = async (messages, userMessage, conversationContext = null) => {
  try {
    console.log(`[AI Service] Processing ${messages.length} messages for context`);
    console.log(`[AI Service] User message: ${userMessage}`);
    console.log(`[AI Service] Conversation context:`, conversationContext);

    const conversationHistory = messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    // Extract previous search terms for better context
    const previousSearchTerms = messages
      .filter(msg => msg.searchTermGenerated)
      .map(msg => msg.searchTermGenerated)
      .slice(-3); // Last 3 search terms

    // Extract product categories and attributes from conversation
    const productContext = extractProductContext(messages, userMessage);

    // Merge with stored conversation context if available
    const enhancedContext = mergeContexts(productContext, conversationContext);

    console.log(`[AI Service] Conversation history:`, conversationHistory);
    console.log(`[AI Service] Previous search terms:`, previousSearchTerms);
    console.log(`[AI Service] Enhanced context:`, enhancedContext);

    const systemPrompt = `You are an AI shopping assistant that helps users find products. Your task is to:
1. Analyze the conversation history and the user's latest message
2. Generate an optimized search term for product search that builds upon previous context
3. Provide a helpful response to continue the conversation

ENHANCED CONTEXT RULES:
- Build upon previous search terms contextually and intelligently
- Identify product categories, attributes (color, size, price, brand), and user preferences
- For refinements: if previous search was "jeans" and user says "black skinny under $50", generate "black skinny jeans under 50"
- For new categories: if user switches from "shirts" to "shoes", start fresh with "shoes" context
- Maintain relevant attributes across searches (e.g., size preferences, price ranges)
- Recognize intent: searching, filtering, comparing, or asking questions

SEARCH TERM OPTIMIZATION:
- Keep search terms concise but descriptive (2-6 words)
- Include key attributes: category + color/style + size/fit + price range
- Use natural product terminology that matches e-commerce searches
- Prioritize most recent and relevant context
- For price mentions: convert "under $50" to "under 50", "less than $100" to "under 100"
- For size mentions: include size when relevant ("large shirts", "size 32 jeans")

CONVERSATION FLOW:
- Acknowledge what the user is looking for
- Explain what you're searching for based on their input
- Ask clarifying questions when context is ambiguous
- Provide helpful suggestions for refinement
- Reference previous searches when building on context

CONTEXT AWARENESS:
- If user mentions colors without category, use the most recent category from conversation
- If user mentions price without category, apply to current search context
- If user asks questions, provide helpful information and suggest searches
- If user compares items, help them refine their criteria

RESPONSE EXAMPLES:
- "I'll search for black skinny jeans under $50 based on your preferences."
- "Looking for blue running shoes to add to your athletic wear collection."
- "Searching for medium-sized hoodies since you mentioned that size preference earlier."

Return your response in this JSON format:
{
  "searchTerm": "optimized search term",
  "response": "conversational response to user",
  "context": {
    "category": "product category if identified",
    "attributes": ["list", "of", "key", "attributes"],
    "intent": "search|refine|compare|question",
    "confidence": 0.8
  }
}

${conversationHistory.length > 0 ? 'IMPORTANT: Use the conversation history to understand the context and build upon previous searches intelligently. Reference the user\'s search journey.' : 'This is the first message in the conversation.'}`;

    // Build enhanced context-aware user message
    const contextualUserMessage = buildContextualMessage(previousSearchTerms, enhancedContext, userMessage);

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: systemPrompt },
        ...conversationHistory,
        { role: "user", content: contextualUserMessage }
      ],
      temperature: 0.3,
      max_tokens: 400,
    });

    const aiResponseContent = response.choices[0]?.message?.content?.trim();
    console.log(`[AI Service] Raw AI response:`, aiResponseContent);

    const result = JSON.parse(aiResponseContent);
    console.log(`[AI Service] Parsed result:`, result);

    return {
      searchTerm: result.searchTerm,
      response: result.response,
      context: result.context || {}
    };
  } catch (error) {
    console.error("AI Chat Service error:", error);
    console.error("Error details:", error.message);

    // Enhanced fallback response
    const fallbackSearchTerm = generateFallbackSearchTerm(messages, userMessage, conversationContext);

    return {
      searchTerm: fallbackSearchTerm,
      response: "I'll help you find products related to your request. Let me search for that.",
      context: { intent: "search" }
    };
  }
};

export const generateConversationTitle = async (initialMessage) => {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "Generate a short, descriptive title (3-6 words) for a shopping conversation based on the user's initial search. Return only the title, no quotes or extra text."
        },
        {
          role: "user",
          content: initialMessage
        }
      ],
      temperature: 0.5,
      max_tokens: 20,
    });

    return response.choices[0]?.message?.content?.trim() || "Product Search";
  } catch (error) {
    console.error("Title generation error:", error);
    return "Product Search";
  }
};

export const generateContextualResponse = async (messages, searchResults) => {
  try {
    const conversationHistory = messages.slice(-5).map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    const systemPrompt = `You are a helpful shopping assistant. Based on the conversation history and search results, provide a brief, conversational response that:
1. Acknowledges what was found
2. Highlights key insights about the products
3. Asks a follow-up question to help refine the search

Keep the response concise (1-2 sentences) and conversational.`;

    const searchSummary = `Found ${searchResults.length} products. Price range: ${searchResults.length > 0 ? 'Various prices available' : 'No products found'}.`;

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: systemPrompt },
        ...conversationHistory,
        { role: "user", content: `Search results: ${searchSummary}` }
      ],
      temperature: 0.7,
      max_tokens: 100,
    });

    return response.choices[0]?.message?.content?.trim() || "I found some products for you. Would you like to refine your search?";
  } catch (error) {
    console.error("Contextual response error:", error);
    return "I found some products for you. Would you like to refine your search?";
  }
};
